<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logcipher - Your Ultimate Writing Companion</title>
    <meta name="description"
        content="AI-powered creative writing tools for writers, students, content creators, and screenwriters. Generate stories, dialogues, poems, and titles with advanced AI technology.">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="icon" type="image/svg+xml" href="images/favicon.svg">
</head>

<body>
    <!-- Header with Navigation -->
    <header class="header">
        <div class="header-background"></div>
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <div class="logo-icon">✨</div>
                    <div class="logo-text">
                        <h1>Logcipher</h1>
                        <span class="logo-tagline">Unleash Your Creativity</span>
                    </div>
                </div>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#home" class="nav-link active">
                            <span class="nav-icon">🏠</span>
                            <span class="nav-text">Home</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#tools" class="nav-link">
                            <span class="nav-icon">🛠️</span>
                            <span class="nav-text">Tools</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="about.html" class="nav-link">
                            <span class="nav-icon">ℹ️</span>
                            <span class="nav-text">About Us</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="contact.html" class="nav-link">
                            <span class="nav-icon">📧</span>
                            <span class="nav-text">Contact Us</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="privacy.html" class="nav-link">
                            <span class="nav-icon">🔒</span>
                            <span class="nav-text">Privacy Policy</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="terms.html" class="nav-link">
                            <span class="nav-icon">📋</span>
                            <span class="nav-text">Terms of Service</span>
                        </a>
                    </li>
                </ul>
                <div class="nav-toggle" id="mobile-menu">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-background">
            <div class="hero-particles"></div>
            <div class="hero-gradient-orb hero-orb-1"></div>
            <div class="hero-gradient-orb hero-orb-2"></div>
            <div class="hero-gradient-orb hero-orb-3"></div>
        </div>
        <div class="hero-container">
            <div class="hero-content">
                <div class="hero-badge">
                    <span class="badge-icon">🚀</span>
                    <span class="badge-text">Powered by Advanced AI</span>
                </div>
                <h1 class="hero-title">Transform Your Ideas Into <span class="gradient-text">Compelling Stories</span>
                </h1>
                <p class="hero-description">Unleash your creativity with our suite of AI-powered writing tools. Whether
                    you're crafting the next bestseller, writing engaging dialogue, composing beautiful poetry, or
                    creating catchy titles, Logcipher is your ultimate creative companion.</p>

                <div class="hero-stats">
                    <div class="stat-item">
                        <div class="stat-number">4</div>
                        <div class="stat-label">AI Tools</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">∞</div>
                        <div class="stat-label">Possibilities</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">24/7</div>
                        <div class="stat-label">Available</div>
                    </div>
                </div>

                <div class="hero-buttons">
                    <a href="#tools" class="btn btn-primary">
                        <span class="btn-icon">✨</span>
                        <span class="btn-text">Start Creating</span>
                    </a>

                </div>

                <div class="hero-features">
                    <div class="feature-item">
                        <span class="feature-icon">⚡</span>
                        <span class="feature-text">Instant Generation</span>
                    </div>
                    <div class="feature-item">
                        <span class="feature-icon">🎨</span>
                        <span class="feature-text">Multiple Genres</span>
                    </div>
                    <div class="feature-item">
                        <span class="feature-icon">📱</span>
                        <span class="feature-text">Mobile Friendly</span>
                    </div>
                </div>
            </div>
            <div class="hero-visual">
                <div class="hero-showcase">
                    <div class="showcase-card showcase-story">
                        <div class="card-header">
                            <span class="card-icon">�</span>
                            <span class="card-title">Story Generator</span>
                        </div>
                        <div class="card-preview">
                            <p>"Once upon a time, in a world where magic flowed like rivers..."</p>
                        </div>
                        <div class="card-tags">
                            <span class="tag">Fantasy</span>
                            <span class="tag">Adventure</span>
                        </div>
                    </div>

                    <div class="showcase-card showcase-dialogue">
                        <div class="card-header">
                            <span class="card-icon">💬</span>
                            <span class="card-title">Dialogue Creator</span>
                        </div>
                        <div class="card-preview">
                            <p>"Detective, we need to talk." <br>"I'm listening..."</p>
                        </div>
                        <div class="card-tags">
                            <span class="tag">Drama</span>
                            <span class="tag">Mystery</span>
                        </div>
                    </div>

                    <div class="showcase-card showcase-poetry">
                        <div class="card-header">
                            <span class="card-icon">🎭</span>
                            <span class="card-title">Poetry Maker</span>
                        </div>
                        <div class="card-preview">
                            <p>Whispers of the wind,<br>Dancing through autumn leaves,<br>Time's gentle embrace.</p>
                        </div>
                        <div class="card-tags">
                            <span class="tag">Haiku</span>
                            <span class="tag">Nature</span>
                        </div>
                    </div>

                    <div class="showcase-card showcase-title">
                        <div class="card-header">
                            <span class="card-icon">📰</span>
                            <span class="card-title">Title Generator</span>
                        </div>
                        <div class="card-preview">
                            <p>"10 Secrets to Boost Your Creativity Today"</p>
                        </div>
                        <div class="card-tags">
                            <span class="tag">Blog</span>
                            <span class="tag">SEO</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Showcase Section -->
    <section class="showcase-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Creative Showcase</h2>
                <p class="section-description">Discover amazing content created with Logcipher by our community
                    of writers and creators</p>
            </div>

            <div class="showcase-grid">
                <div class="showcase-item">
                    <div class="showcase-category">Story</div>
                    <h3 class="showcase-title">The Digital Dreamer</h3>
                    <p class="showcase-excerpt">"In a world where thoughts could be downloaded, Maya discovered that her
                        dreams were worth more than gold. The neural interface hummed softly as she prepared to share
                        her most precious memory..."</p>
                    <div class="showcase-meta">
                        <span class="showcase-genre">Sci-Fi</span>
                        <span class="showcase-length">450 words</span>
                    </div>
                </div>

                <div class="showcase-item">
                    <div class="showcase-category">Poetry</div>
                    <h3 class="showcase-title">Whispers of Autumn</h3>
                    <p class="showcase-excerpt">Golden leaves dance on morning breeze,<br>
                        Time's gentle hand through ancient trees,<br>
                        Each falling leaf a memory,<br>
                        Of seasons past and yet to be.</p>
                    <div class="showcase-meta">
                        <span class="showcase-genre">Nature</span>
                        <span class="showcase-length">Rhyming</span>
                    </div>
                </div>

                <div class="showcase-item">
                    <div class="showcase-category">Dialogue</div>
                    <h3 class="showcase-title">The Last Conversation</h3>
                    <p class="showcase-excerpt">"Detective, we need to talk."<br>
                        "I'm listening, but make it quick."<br>
                        "The evidence we found... it changes everything."<br>
                        "What do you mean?"<br>
                        "The killer isn't who we thought."</p>
                    <div class="showcase-meta">
                        <span class="showcase-genre">Mystery</span>
                        <span class="showcase-length">2 Characters</span>
                    </div>
                </div>

                <div class="showcase-item">
                    <div class="showcase-category">Title</div>
                    <h3 class="showcase-title">Blog Title Collection</h3>
                    <p class="showcase-excerpt">• "10 Secrets to Boost Your Creativity Today"<br>
                        • "The Ultimate Guide to Creative Writing"<br>
                        • "How AI is Revolutionizing Content Creation"<br>
                        • "From Blank Page to Bestseller: A Writer's Journey"</p>
                    <div class="showcase-meta">
                        <span class="showcase-genre">Blog</span>
                        <span class="showcase-length">SEO-Friendly</span>
                    </div>
                </div>

                <div class="showcase-item">
                    <div class="showcase-category">Story</div>
                    <h3 class="showcase-title">The Enchanted Library</h3>
                    <p class="showcase-excerpt">"Once upon a time, in a library where books chose their readers, young
                        Emma discovered a tome that glowed with inner light. As she opened it, the words began to dance
                        off the pages..."</p>
                    <div class="showcase-meta">
                        <span class="showcase-genre">Fantasy</span>
                        <span class="showcase-length">320 words</span>
                    </div>
                </div>

                <div class="showcase-item">
                    <div class="showcase-category">Poetry</div>
                    <h3 class="showcase-title">Digital Hearts</h3>
                    <p class="showcase-excerpt">Pixels and code,<br>
                        Love in binary mode,<br>
                        Hearts connect through screens,<br>
                        In digital dreams,<br>
                        Distance fades away.</p>
                    <div class="showcase-meta">
                        <span class="showcase-genre">Modern</span>
                        <span class="showcase-length">Haiku Style</span>
                    </div>
                </div>
            </div>

            <div class="showcase-cta">
                <p>Ready to create your own masterpiece?</p>
                <a href="#tools" class="btn btn-primary">
                    <span class="btn-icon">✨</span>
                    Start Creating Now
                </a>
            </div>
        </div>
    </section>
    <!-- Usage Instructions Section -->
    <section class="usage-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">How to Use Logcipher</h2>
                <p class="section-description">Follow these simple steps to turn your ideas into polished writing with
                    our AI tools</p>
            </div>

            <div class="usage-steps">
                <div class="usage-step">
                    <div class="usage-icon">📝</div>
                    <h3>1. Describe Your Idea</h3>
                    <p>Tell the AI what you want: a story, dialogue, poem, or catchy titles. Add genre, style, or tone
                        if you like.</p>
                </div>
                <div class="usage-step">
                    <div class="usage-icon">⚙️</div>
                    <h3>2. Choose Options</h3>
                    <p>Select length, style, scene type, characters, themes and more to guide the generation.</p>
                </div>
                <div class="usage-step">
                    <div class="usage-icon">✨</div>
                    <h3>3. Generate & Refine</h3>
                    <p>Click Generate to get results in seconds. Copy, tweak, or try again to perfect your content.</p>
                </div>
            </div>

            <div class="usage-tips">
                <div class="tip">
                    <div class="tip-icon">💡</div>
                    <div class="tip-content">
                        <h4>Pro Tip</h4>
                        <p>Great prompts make great results. Be specific about characters, setting, mood, and outcome.
                        </p>
                    </div>
                </div>
                <div class="tip">
                    <div class="tip-icon">🔁</div>
                    <div class="tip-content">
                        <h4>Iterate</h4>
                        <p>Regenerate with small prompt updates to explore variations and pick the best version.</p>
                    </div>
                </div>
                <div class="tip">
                    <div class="tip-icon">🔒</div>
                    <div class="tip-content">
                        <h4>Privacy</h4>
                        <p>Your inputs are processed for generation and not stored by us. See our Privacy Policy for
                            details.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Tools Section -->
    <section id="tools" class="tools-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">AI Writing Tools</h2>
                <p class="section-description">Choose from our powerful suite of AI-powered writing tools designed for
                    different creative needs</p>
            </div>

            <!-- Tool Navigation Tabs -->
            <div class="tool-tabs">
                <button class="tool-tab active" data-tool="story" onclick="switchTool('story')">
                    <span class="tab-icon">📚</span>
                    <span class="tab-text">Story Generator</span>
                </button>
                <button class="tool-tab" data-tool="dialogue" onclick="switchTool('dialogue')">
                    <span class="tab-icon">💬</span>
                    <span class="tab-text">Dialogue Creator</span>
                </button>
                <button class="tool-tab" data-tool="poetry" onclick="switchTool('poetry')">
                    <span class="tab-icon">🎭</span>
                    <span class="tab-text">Poetry Maker</span>
                </button>
                <button class="tool-tab" data-tool="title" onclick="switchTool('title')">
                    <span class="tab-icon">📰</span>
                    <span class="tab-text">Title Generator</span>
                </button>
            </div>

            <!-- Tool Content Area -->
            <div class="tool-content-area">
                <!-- Story Generator -->
                <div class="tool-panel active" id="panel-story">
                    <div class="panel-header">
                        <div class="panel-info">
                            <h3 class="panel-title">AI Story Generator</h3>
                            <p class="panel-description">Create engaging stories across multiple genres including fairy
                                tales, mystery, and science fiction</p>
                            <div class="panel-tags">
                                <span class="tag">Fairy Tale</span>
                                <span class="tag">Mystery</span>
                                <span class="tag">Sci-Fi</span>
                                <span class="tag">Romance</span>
                            </div>
                        </div>
                    </div>

                    <div class="panel-workspace">
                        <div class="workspace-grid">
                            <div class="workspace-input">
                                <label for="prompt-story">Enter your story prompt:</label>
                                <textarea id="prompt-story"
                                    placeholder="Describe the story you want to create (e.g., 'A magical adventure about a young wizard discovering their powers')"></textarea>

                                <div class="input-row">
                                    <div class="input-group">
                                        <label for="genre-story">Genre:</label>
                                        <select id="genre-story">
                                            <option value="">Select Genre</option>
                                            <option value="Fantasy">Fantasy</option>
                                            <option value="Mystery">Mystery</option>
                                            <option value="Sci-Fi">Sci-Fi</option>
                                            <option value="Romance">Romance</option>
                                            <option value="Horror">Horror</option>
                                            <option value="Adventure">Adventure</option>
                                        </select>
                                    </div>
                                    <div class="input-group">
                                        <label for="length-story">Length:</label>
                                        <select id="length-story">
                                            <option value="">Select Length</option>
                                            <option value="Short (100-200 words)">Short</option>
                                            <option value="Medium (300-500 words)">Medium</option>
                                            <option value="Long (500-800 words)">Long</option>
                                        </select>
                                    </div>
                                </div>

                                <button class="btn btn-generate" onclick="generateContent('story')">
                                    <span class="btn-icon">✨</span>
                                    <span class="btn-text">Generate Story</span>
                                    <span class="btn-loading" style="display: none;">
                                        <span class="spinner-small"></span>
                                        Generating...
                                    </span>
                                </button>
                            </div>

                            <div class="workspace-output">
                                <div class="output-header">
                                    <h4>Generated Content</h4>
                                    <div class="output-actions">
                                        <button class="btn-action" onclick="copyContent('story')"
                                            title="Copy to clipboard">
                                            <span class="action-icon">📋</span>
                                        </button>
                                        <button class="btn-action" onclick="clearContent('story')"
                                            title="Clear content">
                                            <span class="action-icon">🗑️</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="result-container">
                                    <div class="loading" id="loading-story" style="display: none;">
                                        <div class="spinner"></div>
                                        <p>Creating your story...</p>
                                    </div>
                                    <div class="result" id="result-story"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Story Generator -->


                <!-- Dialogue Generator -->
                <div class="tool-panel" id="panel-dialogue">
                    <div class="panel-header">
                        <div class="panel-info">
                            <h3 class="panel-title">Dialogue Generator</h3>
                            <p class="panel-description">Perfect for screenwriters and playwrights to create natural,
                                compelling character conversations</p>
                            <div class="panel-tags">
                                <span class="tag">Drama</span>
                                <span class="tag">Comedy</span>
                                <span class="tag">Action</span>
                                <span class="tag">Thriller</span>
                            </div>
                        </div>
                    </div>

                    <div class="panel-workspace">
                        <div class="workspace-grid">
                            <div class="workspace-input">
                                <label for="prompt-dialogue">Enter your dialogue prompt:</label>
                                <textarea id="prompt-dialogue"
                                    placeholder="Describe the dialogue scene (e.g., 'Two detectives discussing a mysterious case in a coffee shop')"></textarea>

                                <div class="input-row">
                                    <div class="input-group">
                                        <label for="scene-dialogue">Scene Type:</label>
                                        <select id="scene-dialogue">
                                            <option value="">Select Scene</option>
                                            <option value="Dramatic">Dramatic</option>
                                            <option value="Comedy">Comedy</option>
                                            <option value="Action">Action</option>
                                            <option value="Romance">Romance</option>
                                            <option value="Thriller">Thriller</option>
                                        </select>
                                    </div>
                                    <div class="input-group">
                                        <label for="characters-dialogue">Characters:</label>
                                        <select id="characters-dialogue">
                                            <option value="">Select Characters</option>
                                            <option value="2 Characters">2 Characters</option>
                                            <option value="3 Characters">3 Characters</option>
                                            <option value="4+ Characters">4+ Characters</option>
                                        </select>
                                    </div>
                                </div>

                                <button class="btn btn-generate" onclick="generateContent('dialogue')">
                                    <span class="btn-icon">💬</span>
                                    <span class="btn-text">Create Dialogue</span>
                                    <span class="btn-loading" style="display: none;">
                                        <span class="spinner-small"></span>
                                        Generating...
                                    </span>
                                </button>
                            </div>

                            <div class="workspace-output">
                                <div class="output-header">
                                    <h4>Generated Content</h4>
                                    <div class="output-actions">
                                        <button class="btn-action" onclick="copyContent('dialogue')"
                                            title="Copy to clipboard">
                                            <span class="action-icon">📋</span>
                                        </button>
                                        <button class="btn-action" onclick="clearContent('dialogue')"
                                            title="Clear content">
                                            <span class="action-icon">🗑️</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="result-container">
                                    <div class="loading" id="loading-dialogue" style="display: none;">
                                        <div class="spinner"></div>
                                        <p>Creating your dialogue...</p>
                                    </div>
                                    <div class="result" id="result-dialogue"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Poetry Generator -->
                <div class="tool-panel" id="panel-poetry">
                    <div class="panel-header">
                        <div class="panel-info">
                            <h3 class="panel-title">Poetry Generator</h3>
                            <p class="panel-description">Craft beautiful poems with various themes and styles,
                                supporting both English and multilingual options</p>
                            <div class="panel-tags">
                                <span class="tag">Love</span>
                                <span class="tag">Nature</span>
                                <span class="tag">Life</span>
                                <span class="tag">Abstract</span>
                            </div>
                        </div>
                    </div>

                    <div class="panel-workspace">
                        <div class="workspace-grid">
                            <div class="workspace-input">
                                <label for="prompt-poetry">Enter your poetry prompt:</label>
                                <textarea id="prompt-poetry"
                                    placeholder="Describe the poem theme or emotion (e.g., 'A poem about autumn leaves and the passage of time')"></textarea>

                                <div class="input-row">
                                    <div class="input-group">
                                        <label for="style-poetry">Style:</label>
                                        <select id="style-poetry">
                                            <option value="">Select Style</option>
                                            <option value="Free Verse">Free Verse</option>
                                            <option value="Rhyming">Rhyming</option>
                                            <option value="Haiku">Haiku</option>
                                            <option value="Sonnet">Sonnet</option>
                                            <option value="Limerick">Limerick</option>
                                        </select>
                                    </div>
                                    <div class="input-group">
                                        <label for="theme-poetry">Theme:</label>
                                        <select id="theme-poetry">
                                            <option value="">Select Theme</option>
                                            <option value="Love">Love</option>
                                            <option value="Nature">Nature</option>
                                            <option value="Life">Life</option>
                                            <option value="Dreams">Dreams</option>
                                            <option value="Hope">Hope</option>
                                        </select>
                                    </div>
                                </div>

                                <button class="btn btn-generate" onclick="generateContent('poetry')">
                                    <span class="btn-icon">🎭</span>
                                    <span class="btn-text">Write Poetry</span>
                                    <span class="btn-loading" style="display: none;">
                                        <span class="spinner-small"></span>
                                        Generating...
                                    </span>
                                </button>
                            </div>

                            <div class="workspace-output">
                                <div class="output-header">
                                    <h4>Generated Content</h4>
                                    <div class="output-actions">
                                        <button class="btn-action" onclick="copyContent('poetry')"
                                            title="Copy to clipboard">
                                            <span class="action-icon">📋</span>
                                        </button>
                                        <button class="btn-action" onclick="clearContent('poetry')"
                                            title="Clear content">
                                            <span class="action-icon">🗑️</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="result-container">
                                    <div class="loading" id="loading-poetry" style="display: none;">
                                        <div class="spinner"></div>
                                        <p>Creating your poem...</p>
                                    </div>
                                    <div class="result" id="result-poetry"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Title Generator -->
                <div class="tool-panel" id="panel-title">
                    <div class="panel-header">
                        <div class="panel-info">
                            <h3 class="panel-title">Title Generator</h3>
                            <p class="panel-description">Generate catchy, SEO-friendly titles for articles, blogs,
                                books, and creative content</p>
                            <div class="panel-tags">
                                <span class="tag">Blog Posts</span>
                                <span class="tag">Articles</span>
                                <span class="tag">Books</span>
                                <span class="tag">Headlines</span>
                            </div>
                        </div>
                    </div>

                    <div class="panel-workspace">
                        <div class="workspace-grid">
                            <div class="workspace-input">
                                <label for="prompt-title">Enter your content description:</label>
                                <textarea id="prompt-title"
                                    placeholder="Describe your content (e.g., 'A blog post about sustainable living tips for beginners')"></textarea>

                                <div class="input-row">
                                    <div class="input-group">
                                        <label for="type-title">Content Type:</label>
                                        <select id="type-title">
                                            <option value="">Select Type</option>
                                            <option value="Blog Post">Blog Post</option>
                                            <option value="Article">Article</option>
                                            <option value="Book">Book</option>
                                            <option value="Story">Story</option>
                                            <option value="Newsletter">Newsletter</option>
                                        </select>
                                    </div>
                                    <div class="input-group">
                                        <label for="style-title">Style:</label>
                                        <select id="style-title">
                                            <option value="">Select Style</option>
                                            <option value="Catchy">Catchy</option>
                                            <option value="Professional">Professional</option>
                                            <option value="Creative">Creative</option>
                                            <option value="SEO-Friendly">SEO-Friendly</option>
                                            <option value="Question-Based">Question-Based</option>
                                        </select>
                                    </div>
                                </div>

                                <button class="btn btn-generate" onclick="generateContent('title')">
                                    <span class="btn-icon">📰</span>
                                    <span class="btn-text">Generate Titles</span>
                                    <span class="btn-loading" style="display: none;">
                                        <span class="spinner-small"></span>
                                        Generating...
                                    </span>
                                </button>
                            </div>

                            <div class="workspace-output">
                                <div class="output-header">
                                    <h4>Generated Content</h4>
                                    <div class="output-actions">
                                        <button class="btn-action" onclick="copyContent('title')"
                                            title="Copy to clipboard">
                                            <span class="action-icon">📋</span>
                                        </button>
                                        <button class="btn-action" onclick="clearContent('title')"
                                            title="Clear content">
                                            <span class="action-icon">🗑️</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="result-container">
                                    <div class="loading" id="loading-title" style="display: none;">
                                        <div class="spinner"></div>
                                        <p>Creating your titles...</p>
                                    </div>
                                    <div class="result" id="result-title"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- Testimonials Section -->
    <section class="testimonials-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">What Users Say</h2>
                <p class="section-description">Real feedback from writers, students, and creators using Logcipher</p>
            </div>

            <div class="testimonials-grid">
                <div class="testimonial-card">
                    <div class="avatar">🧑‍🎓</div>
                    <div class="stars" aria-label="5 out of 5">★★★★★</div>
                    <p class="quote">“Logcipher helped me finish my short story in one night. The prompts and rewrites
                        were spot on.”</p>
                    <div class="author">— Mia, Student</div>
                </div>

                <div class="testimonial-card">
                    <div class="avatar">🧑‍💻</div>
                    <div class="stars" aria-label="5 out of 5">★★★★★</div>
                    <p class="quote">“The dialogue tool is a game changer for my scripts. Natural, engaging
                        conversations in seconds.”</p>
                    <div class="author">— Devin, Screenwriter</div>
                </div>

                <div class="testimonial-card">
                    <div class="avatar">🧑‍🎨</div>
                    <div class="stars" aria-label="4.5 out of 5">★★★★☆</div>
                    <p class="quote">“Love the poetry generator. It sparks ideas I never thought of and helps me refine
                        my style.”</p>
                    <div class="author">— Alina, Creator</div>
                </div>

                <div class="testimonial-card">
                    <div class="avatar">🧑‍💼</div>
                    <div class="stars" aria-label="5 out of 5">★★★★★</div>
                    <p class="quote">“Title Generator boosted our blog CTR by 30%. Quick, relevant, and SEO-friendly.”
                    </p>
                    <div class="author">— Ryan, Content Manager</div>
                </div>
            </div>
        </div>
    </section>







    <!-- Features Section -->
    <section class="features-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Why Choose Logcipher?</h2>
            </div>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🚀</div>
                    <h3>Advanced AI Technology</h3>
                    <p>Powered by cutting-edge AI models for high-quality, creative content generation</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h3>Lightning Fast</h3>
                    <p>Generate content in seconds, not hours. Perfect for tight deadlines and creative flow</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🎨</div>
                    <h3>Multiple Genres</h3>
                    <p>Support for various writing styles and genres to match your creative vision</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📱</div>
                    <h3>Mobile Friendly</h3>
                    <p>Write anywhere, anytime with our responsive design that works on all devices</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-main">
                    <div class="footer-brand">
                        <div class="brand-info">
                            <h3 class="brand-name">Logcipher.<span class="brand-highlight">com</span></h3>
                            <p class="brand-description">Professional AI-powered writing tools designed for accuracy and
                                creativity. All content generation is performed locally in your browser for maximum
                                privacy and speed.</p>
                        </div>
                        <div class="social-icons">
                            <a href="https://twitter.com" target="_blank" class="social-icon" aria-label="Twitter">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path
                                        d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                                </svg>
                            </a>
                            <a href="https://github.com" target="_blank" class="social-icon" aria-label="GitHub">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path
                                        d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
                                </svg>
                            </a>
                            <a href="https://linkedin.com" target="_blank" class="social-icon" aria-label="LinkedIn">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path
                                        d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                                </svg>
                            </a>
                            <a href="https://discord.com" target="_blank" class="social-icon" aria-label="Discord">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path
                                        d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028 14.09 14.09 0 0 0 1.226-1.994.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z" />
                                </svg>
                            </a>
                            <a href="https://reddit.com" target="_blank" class="social-icon" aria-label="Reddit">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path
                                        d="M12 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0zm5.01 4.744c.688 0 1.25.561 1.25 1.249a1.25 1.25 0 0 1-2.498.056l-2.597-.547-.8 3.747c1.824.07 3.48.632 4.674 1.488.308-.309.73-.491 1.207-.491.968 0 1.754.786 1.754 1.754 0 .716-.435 1.333-1.01 1.614a3.111 3.111 0 0 1 .042.52c0 2.694-3.13 4.87-7.004 4.87-3.874 0-7.004-2.176-7.004-4.87 0-.183.015-.366.043-.534A1.748 1.748 0 0 1 4.028 12c0-.968.786-1.754 1.754-1.754.463 0 .898.196 1.207.49 1.207-.883 2.878-1.43 4.744-1.487l.885-4.182a.342.342 0 0 1 .14-.197.35.35 0 0 1 .238-.042l2.906.617a1.214 1.214 0 0 1 1.108-.701zM9.25 12C8.561 12 8 12.562 8 13.25c0 .687.561 1.248 1.25 1.248.687 0 1.248-.561 1.248-1.249 0-.688-.561-1.249-1.249-1.249zm5.5 0c-.687 0-1.248.561-1.248 1.25 0 .687.561 1.248 1.249 1.248.688 0 1.249-.561 1.249-1.249 0-.687-.562-1.249-1.25-1.249zm-5.466 3.99a.327.327 0 0 0-.231.094.33.33 0 0 0 0 .463c.842.842 2.484.913 2.961.913.477 0 2.105-.056 2.961-.913a.361.361 0 0 0 .029-.463.33.33 0 0 0-.464 0c-.547.533-1.684.73-2.512.73-.828 0-1.979-.196-2.512-.73a.326.326 0 0 0-.232-.095z" />
                                </svg>
                            </a>
                        </div>
                    </div>
                    <div class="footer-tools">
                        <div class="links-section">
                            <h4>AI Tools</h4>
                            <ul>
                                <li><a href="#tools" onclick="switchTool('story')">Story Generator</a></li>
                                <li><a href="#tools" onclick="switchTool('dialogue')">Dialogue Creator</a></li>
                                <li><a href="#tools" onclick="switchTool('poetry')">Poetry Maker</a></li>
                                <li><a href="#tools" onclick="switchTool('title')">Title Generator</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="footer-links">
                        <div class="links-section">
                            <h4>Quick Links</h4>
                            <ul>
                                <li><a href="index.html">Home</a></li>
                                <li><a href="about.html">About Us</a></li>
                                <li><a href="contact.html">Contact Us</a></li>
                                <li><a href="privacy.html">Privacy Policy</a></li>
                                <li><a href="terms.html">Terms of Service</a></li>
                            </ul>
                        </div>
                    </div>
                </div>

            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 logcipher.com All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
</body>

</html>